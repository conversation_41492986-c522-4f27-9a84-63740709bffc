package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"go-mail/internal/logger"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"golang.org/x/crypto/bcrypt"
	_ "modernc.org/sqlite"
)

// Database 数据库操作结构
type Database struct {
	db     *sql.DB
	logger *logger.Logger
}

// NewDatabase 创建新的数据库实例
func NewDatabase(dbPath string) (*Database, error) {
	// 确保数据库目录存在
	dir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("创建数据库目录失败: %w", err)
	}

	// 构建SQLite连接字符串，启用WAL模式和优化参数
	dsn := fmt.Sprintf("%s?_journal_mode=WAL&_synchronous=NORMAL&_cache_size=10000&_temp_store=memory&_busy_timeout=30000", dbPath)

	// 打开数据库连接
	db, err := sql.Open("sqlite", dsn)
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败: %w", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(25)                 // 最大打开连接数
	db.SetMaxIdleConns(5)                  // 最大空闲连接数
	db.SetConnMaxLifetime(5 * time.Minute) // 连接最大生存时间

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	database := &Database{
		db:     db,
		logger: logger.GetDefaultLogger(),
	}

	// 设置SQLite优化参数
	if err := database.configureSQLite(); err != nil {
		return nil, fmt.Errorf("配置SQLite参数失败: %w", err)
	}

	// 初始化数据库表
	if err := database.initTables(); err != nil {
		return nil, fmt.Errorf("初始化数据库表失败: %w", err)
	}

	return database, nil
}

// configureSQLite 配置SQLite优化参数
func (d *Database) configureSQLite() error {
	// 设置SQLite优化参数
	pragmas := []string{
		"PRAGMA foreign_keys = ON",    // 启用外键约束
		"PRAGMA journal_mode = WAL",   // 启用WAL模式
		"PRAGMA synchronous = NORMAL", // 设置同步模式
		"PRAGMA cache_size = 10000",   // 设置缓存大小
		"PRAGMA temp_store = memory",  // 临时表存储在内存中
		"PRAGMA mmap_size = 51435456", // 设置内存映射大小(512MB)
		"PRAGMA busy_timeout = 30000", // 设置忙等待超时(30秒)
	}

	for _, pragma := range pragmas {
		if _, err := d.db.Exec(pragma); err != nil {
			d.logger.Warn("设置SQLite参数失败", "pragma", pragma, "error", err)
			// 不返回错误，继续执行其他参数设置
		}
	}

	return nil
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	if d.db != nil {
		return d.db.Close()
	}
	return nil
}

// initTables 初始化数据库表
func (d *Database) initTables() error {
	// 创建账户表（包含邮箱管理扩展字段）
	accountTableSQL := `
	CREATE TABLE IF NOT EXISTS accounts (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		email TEXT UNIQUE NOT NULL,
		password TEXT NOT NULL,
		cookie_data TEXT,
		login_status TEXT DEFAULT 'failed',
		last_login_time DATETIME,
		email_status TEXT DEFAULT 'valid',
		usage_status TEXT DEFAULT 'available',
		usage_id TEXT,
		session_id TEXT,
		jsession_id TEXT,
		navigator_sid TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		-- 邮箱管理扩展字段
		batch_import_id TEXT,
		verification_status TEXT DEFAULT 'unverified',
		last_verification_time DATETIME,
		verification_error TEXT,
		is_disabled BOOLEAN DEFAULT FALSE,
		import_source TEXT DEFAULT 'manual',
		tags TEXT
	);`

	if _, err := d.db.Exec(accountTableSQL); err != nil {
		return fmt.Errorf("创建账户表失败: %w", err)
	}

	// 创建登录记录表
	loginRecordSQL := `
	CREATE TABLE IF NOT EXISTS login_records (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		email TEXT NOT NULL,
		login_time DATETIME DEFAULT CURRENT_TIMESTAMP,
		login_status TEXT NOT NULL,
		error_msg TEXT,
		ip_address TEXT,
		user_agent TEXT
	);`

	if _, err := d.db.Exec(loginRecordSQL); err != nil {
		return fmt.Errorf("创建登录记录表失败: %w", err)
	}

	// 创建新增表
	if err := d.createExtendedTables(); err != nil {
		return fmt.Errorf("创建扩展表失败: %w", err)
	}

	// 创建邮箱管理相关表
	if err := d.createMailboxManagementTables(); err != nil {
		return fmt.Errorf("创建邮箱管理表失败: %w", err)
	}

	// 创建索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_accounts_email ON accounts(email);",
		"CREATE INDEX IF NOT EXISTS idx_accounts_usage_status ON accounts(usage_status);",
		"CREATE INDEX IF NOT EXISTS idx_login_records_email ON login_records(email);",
		"CREATE INDEX IF NOT EXISTS idx_login_records_time ON login_records(login_time);",
		// 新增索引
		"CREATE INDEX IF NOT EXISTS idx_activation_codes_code ON activation_codes(code);",
		"CREATE INDEX IF NOT EXISTS idx_activation_codes_status ON activation_codes(status);",
		"CREATE INDEX IF NOT EXISTS idx_activation_codes_device ON activation_codes(device_fingerprint);",
		"CREATE INDEX IF NOT EXISTS idx_temp_mailboxes_address ON temp_mailboxes(mailbox_address);",
		"CREATE INDEX IF NOT EXISTS idx_temp_mailboxes_device ON temp_mailboxes(device_fingerprint);",
		"CREATE INDEX IF NOT EXISTS idx_temp_mailboxes_status ON temp_mailboxes(status);",
		"CREATE INDEX IF NOT EXISTS idx_mail_records_mailbox ON mail_records(mailbox_id);",
		"CREATE INDEX IF NOT EXISTS idx_admin_users_username ON admin_users(username);",
		"CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(key);",
		// 邮箱管理索引
		"CREATE INDEX IF NOT EXISTS idx_accounts_batch_import ON accounts(batch_import_id);",
		"CREATE INDEX IF NOT EXISTS idx_accounts_verification_status ON accounts(verification_status);",
		"CREATE INDEX IF NOT EXISTS idx_accounts_is_disabled ON accounts(is_disabled);",
		"CREATE INDEX IF NOT EXISTS idx_batch_operations_status ON batch_operations(status);",
		"CREATE INDEX IF NOT EXISTS idx_batch_operations_type ON batch_operations(operation_type);",
		"CREATE INDEX IF NOT EXISTS idx_verification_tasks_status ON mailbox_verification_tasks(status);",
		"CREATE INDEX IF NOT EXISTS idx_scheduler_status_name ON task_scheduler_status(task_name);",
		"CREATE INDEX IF NOT EXISTS idx_mailbox_stats_date ON mailbox_statistics(stat_date);",
	}

	for _, indexSQL := range indexes {
		if _, err := d.db.Exec(indexSQL); err != nil {
			return fmt.Errorf("创建索引失败: %w", err)
		}
	}

	return nil
}

// createExtendedTables 创建扩展数据库表
func (d *Database) createExtendedTables() error {
	// 创建激活码表
	activationCodesSQL := `
	CREATE TABLE IF NOT EXISTS activation_codes (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		code TEXT UNIQUE NOT NULL,
		device_fingerprint TEXT,
		mac_address TEXT,
		status TEXT DEFAULT 'unused',
		expires_at DATETIME NOT NULL,
		used_at DATETIME,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		description TEXT,
		batch_id TEXT
	);`

	if _, err := d.db.Exec(activationCodesSQL); err != nil {
		return fmt.Errorf("创建激活码表失败: %w", err)
	}

	// 创建临时邮箱表
	tempMailboxesSQL := `
	CREATE TABLE IF NOT EXISTS temp_mailboxes (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		mailbox_address TEXT NOT NULL,
		account_email TEXT NOT NULL,
		device_fingerprint TEXT NOT NULL,
		activation_code TEXT NOT NULL,
		status TEXT DEFAULT 'active',
		allocated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		last_check_at DATETIME,
		expires_at DATETIME NOT NULL,
		auto_release BOOLEAN DEFAULT TRUE,
		release_reason TEXT,
		FOREIGN KEY (account_email) REFERENCES accounts(email)
	);`

	if _, err := d.db.Exec(tempMailboxesSQL); err != nil {
		return fmt.Errorf("创建临时邮箱表失败: %w", err)
	}

	// 创建邮件记录表
	mailRecordsSQL := `
	CREATE TABLE IF NOT EXISTS mail_records (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		mailbox_id INTEGER NOT NULL,
		mail_id TEXT NOT NULL,
		subject TEXT,
		sender TEXT,
		received_at DATETIME NOT NULL,
		content_fetched BOOLEAN DEFAULT FALSE,
		content_text TEXT,
		content_html TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (mailbox_id) REFERENCES temp_mailboxes(id)
	);`

	if _, err := d.db.Exec(mailRecordsSQL); err != nil {
		return fmt.Errorf("创建邮件记录表失败: %w", err)
	}

	// 创建管理员用户表
	adminUsersSQL := `
	CREATE TABLE IF NOT EXISTS admin_users (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username TEXT UNIQUE NOT NULL,
		password TEXT NOT NULL,
		role TEXT DEFAULT 'operator',
		status TEXT DEFAULT 'active',
		last_login DATETIME,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	if _, err := d.db.Exec(adminUsersSQL); err != nil {
		return fmt.Errorf("创建管理员用户表失败: %w", err)
	}

	// 创建系统配置表
	systemConfigSQL := `
	CREATE TABLE IF NOT EXISTS system_config (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		key TEXT UNIQUE NOT NULL,
		value TEXT NOT NULL,
		type TEXT DEFAULT 'string',
		category TEXT DEFAULT 'system',
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	if _, err := d.db.Exec(systemConfigSQL); err != nil {
		return fmt.Errorf("创建系统配置表失败: %w", err)
	}

	// 插入默认管理员账户（如果不存在）
	if err := d.createDefaultAdmin(); err != nil {
		return fmt.Errorf("创建默认管理员失败: %w", err)
	}

	// 插入默认系统配置
	if err := d.createDefaultConfig(); err != nil {
		return fmt.Errorf("创建默认配置失败: %w", err)
	}

	return nil
}

// GetDB 获取数据库连接
func (d *Database) GetDB() *sql.DB {
	return d.db
}

// createDefaultAdmin 创建默认管理员账户
func (d *Database) createDefaultAdmin() error {
	// 检查是否已存在管理员
	var count int
	err := d.db.QueryRow("SELECT COUNT(*) FROM admin_users WHERE username = ?", "admin").Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		return nil // 已存在，跳过
	}

	// 创建默认管理员（密码：admin123的SHA256哈希）
	// 注意：这里使用简单的SHA256，生产环境建议使用bcrypt
	passwordHash := "240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9" // admin123的SHA256
	_, err = d.db.Exec(`
		INSERT INTO admin_users (username, password, role, status)
		VALUES (?, ?, ?, ?)`,
		"admin", passwordHash, "admin", "active")

	return err
}

// createDefaultConfig 创建默认系统配置
func (d *Database) createDefaultConfig() error {
	defaultConfigs := []struct {
		Key      string
		Value    string
		Type     string
		Category string
	}{
		{"mailbox_auto_release_minutes", "3", "int", "mailbox"},
		{"max_concurrent_allocations", "100", "int", "mailbox"},
		{"activation_code_expiry_days", "30", "int", "security"},
		{"jwt_secret", "go-mail-jwt-secret-key", "string", "security"},
		{"aes_key", "go-mail-aes-key-32-bytes-long!!", "string", "security"},
		{"system_name", "Go-Mail临时邮箱服务", "string", "system"},
		{"version", "1.0.0", "string", "system"},
	}

	for _, config := range defaultConfigs {
		// 检查配置是否已存在
		var count int
		err := d.db.QueryRow("SELECT COUNT(*) FROM system_config WHERE key = ?", config.Key).Scan(&count)
		if err != nil {
			return err
		}

		if count == 0 {
			// 插入新配置
			_, err = d.db.Exec(`
				INSERT INTO system_config (key, value, type, category)
				VALUES (?, ?, ?, ?)`,
				config.Key, config.Value, config.Type, config.Category)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// HashPassword 加密密码
func (d *Database) HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("密码加密失败: %w", err)
	}
	return string(hashedBytes), nil
}

// VerifyPassword 验证密码
func (d *Database) VerifyPassword(hashedPassword, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}

// SerializeCookies 序列化cookies为JSON字符串
func (d *Database) SerializeCookies(cookies []*http.Cookie) (string, error) {
	if len(cookies) == 0 {
		return "", nil
	}

	cookieInfos := make([]CookieInfo, len(cookies))
	for i, cookie := range cookies {
		cookieInfos[i] = CookieInfo{
			Name:     cookie.Name,
			Value:    cookie.Value,
			Domain:   cookie.Domain,
			Path:     cookie.Path,
			Expires:  cookie.Expires,
			Secure:   cookie.Secure,
			HttpOnly: cookie.HttpOnly,
		}
	}

	data, err := json.Marshal(cookieInfos)
	if err != nil {
		return "", fmt.Errorf("序列化cookies失败: %w", err)
	}

	return string(data), nil
}

// DeserializeCookies 反序列化JSON字符串为cookies
func (d *Database) DeserializeCookies(cookieData string) ([]*http.Cookie, error) {
	if cookieData == "" {
		return nil, nil
	}

	var cookieInfos []CookieInfo
	if err := json.Unmarshal([]byte(cookieData), &cookieInfos); err != nil {
		return nil, fmt.Errorf("反序列化cookies失败: %w", err)
	}

	cookies := make([]*http.Cookie, len(cookieInfos))
	for i, info := range cookieInfos {
		cookies[i] = &http.Cookie{
			Name:     info.Name,
			Value:    info.Value,
			Domain:   info.Domain,
			Path:     info.Path,
			Expires:  info.Expires,
			Secure:   info.Secure,
			HttpOnly: info.HttpOnly,
		}
	}

	return cookies, nil
}

// SaveAccount 保存或更新账户信息
func (d *Database) SaveAccount(email, password string, cookies []*http.Cookie, sessionID, jsessionID, navigatorSID string) error {
	// 加密密码
	hashedPassword, err := d.HashPassword(password)
	if err != nil {
		return err
	}

	// 序列化cookies
	cookieData, err := d.SerializeCookies(cookies)
	if err != nil {
		return err
	}

	now := time.Now()

	// 检查账户是否已存在
	var exists bool
	checkSQL := "SELECT EXISTS(SELECT 1 FROM accounts WHERE email = ?)"
	if err := d.db.QueryRow(checkSQL, email).Scan(&exists); err != nil {
		return fmt.Errorf("检查账户是否存在失败: %w", err)
	}

	if exists {
		// 更新现有账户
		updateSQL := `
		UPDATE accounts SET
			password = ?,
			cookie_data = ?,
			login_status = ?,
			last_login_time = ?,
			email_status = ?,
			session_id = ?,
			jsession_id = ?,
			navigator_sid = ?,
			updated_at = ?
		WHERE email = ?`

		_, err = d.db.Exec(updateSQL, hashedPassword, cookieData, LoginStatusSuccess, now, EmailStatusValid, sessionID, jsessionID, navigatorSID, now, email)
		if err != nil {
			return fmt.Errorf("更新账户信息失败: %w", err)
		}
	} else {
		// 插入新账户
		insertSQL := `
		INSERT INTO accounts (
			email, password, cookie_data, login_status, last_login_time,
			email_status, usage_status, session_id, jsession_id, navigator_sid,
			created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

		_, err = d.db.Exec(insertSQL, email, hashedPassword, cookieData, LoginStatusSuccess, now, EmailStatusValid, UsageStatusAvailable, sessionID, jsessionID, navigatorSID, now, now)
		if err != nil {
			return fmt.Errorf("插入账户信息失败: %w", err)
		}
	}

	return nil
}

// GetAccount 根据邮箱获取账户信息
func (d *Database) GetAccount(email string) (*AccountRecord, error) {
	query := `
	SELECT id, email, password, cookie_data, login_status, last_login_time,
		   email_status, usage_status, usage_id, session_id, jsession_id,
		   navigator_sid, created_at, updated_at
	FROM accounts WHERE email = ?`

	row := d.db.QueryRow(query, email)

	account := &AccountRecord{}
	err := row.Scan(
		&account.ID, &account.Email, &account.Password, &account.CookieData,
		&account.LoginStatus, &account.LastLoginTime, &account.EmailStatus,
		&account.UsageStatus, &account.UsageID, &account.SessionID,
		&account.JSessionID, &account.NavigatorSID, &account.CreatedAt, &account.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 账户不存在
		}
		return nil, fmt.Errorf("查询账户信息失败: %w", err)
	}

	return account, nil
}

// UpdateAccountUsage 更新账户使用状态
func (d *Database) UpdateAccountUsage(email, usageID string, inUse bool) error {
	status := UsageStatusAvailable
	if inUse {
		status = UsageStatusInUse
	}

	updateSQL := `
	UPDATE accounts SET
		usage_status = ?,
		usage_id = ?,
		updated_at = ?
	WHERE email = ?`

	_, err := d.db.Exec(updateSQL, status, usageID, time.Now(), email)
	if err != nil {
		return fmt.Errorf("更新账户使用状态失败: %w", err)
	}

	return nil
}

// RecordLogin 记录登录尝试
func (d *Database) RecordLogin(email, status, errorMsg, ipAddress, userAgent string) error {
	insertSQL := `
	INSERT INTO login_records (email, login_status, error_msg, ip_address, user_agent)
	VALUES (?, ?, ?, ?, ?)`

	_, err := d.db.Exec(insertSQL, email, status, errorMsg, ipAddress, userAgent)
	if err != nil {
		return fmt.Errorf("记录登录失败: %w", err)
	}

	return nil
}

// GetAvailableAccounts 获取可用的账户列表
func (d *Database) GetAvailableAccounts() ([]*AccountRecord, error) {
	query := `
	SELECT id, email, password, cookie_data, login_status, last_login_time,
		   email_status, usage_status, usage_id, session_id, jsession_id,
		   navigator_sid, created_at, updated_at
	FROM accounts
	WHERE usage_status = ? AND email_status = ? AND login_status = ?
	ORDER BY last_login_time DESC`

	rows, err := d.db.Query(query, UsageStatusAvailable, EmailStatusValid, LoginStatusSuccess)
	if err != nil {
		return nil, fmt.Errorf("查询可用账户失败: %w", err)
	}
	defer rows.Close()

	var accounts []*AccountRecord
	for rows.Next() {
		account := &AccountRecord{}
		err := rows.Scan(
			&account.ID, &account.Email, &account.Password, &account.CookieData,
			&account.LoginStatus, &account.LastLoginTime, &account.EmailStatus,
			&account.UsageStatus, &account.UsageID, &account.SessionID,
			&account.JSessionID, &account.NavigatorSID, &account.CreatedAt, &account.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描账户记录失败: %w", err)
		}
		accounts = append(accounts, account)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历账户记录失败: %w", err)
	}

	return accounts, nil
}

// GetAccountCookies 获取账户的cookies
func (d *Database) GetAccountCookies(email string) ([]*http.Cookie, error) {
	account, err := d.GetAccount(email)
	if err != nil {
		return nil, err
	}
	if account == nil {
		return nil, fmt.Errorf("账户不存在: %s", email)
	}

	return d.DeserializeCookies(account.CookieData)
}

// UpdateLoginStatus 更新登录状态
func (d *Database) UpdateLoginStatus(email, status string, errorMsg string) error {
	var lastLoginTime *time.Time
	if status == LoginStatusSuccess {
		now := time.Now()
		lastLoginTime = &now
	}

	updateSQL := `
	UPDATE accounts SET
		login_status = ?,
		last_login_time = ?,
		updated_at = ?
	WHERE email = ?`

	_, err := d.db.Exec(updateSQL, status, lastLoginTime, time.Now(), email)
	if err != nil {
		return fmt.Errorf("更新登录状态失败: %w", err)
	}

	// 记录登录尝试
	return d.RecordLogin(email, status, errorMsg, "", "")
}

// GetAccountStats 获取账户统计信息
func (d *Database) GetAccountStats() (map[string]int, error) {
	stats := make(map[string]int)

	queries := map[string]string{
		"total":     "SELECT COUNT(*) FROM accounts",
		"valid":     "SELECT COUNT(*) FROM accounts WHERE email_status = 'valid'",
		"invalid":   "SELECT COUNT(*) FROM accounts WHERE email_status = 'invalid'",
		"in_use":    "SELECT COUNT(*) FROM accounts WHERE usage_status = 'in_use'",
		"available": "SELECT COUNT(*) FROM accounts WHERE usage_status = 'available'",
		"success":   "SELECT COUNT(*) FROM accounts WHERE login_status = 'success'",
		"failed":    "SELECT COUNT(*) FROM accounts WHERE login_status = 'failed'",
	}

	for key, query := range queries {
		var count int
		if err := d.db.QueryRow(query).Scan(&count); err != nil {
			return nil, fmt.Errorf("查询%s统计失败: %w", key, err)
		}
		stats[key] = count
	}

	return stats, nil
}

// LogQuery 记录数据库查询日志
func (d *Database) LogQuery(operation, sql string, args []interface{}, duration time.Duration, err error) {
	log := d.logger.WithFields(map[string]any{
		"operation": operation,
		"sql":       sql,
		"args":      args,
		"duration":  duration.String(),
	})

	if err != nil {
		log.WithError(err).Error("数据库查询失败")
	} else {
		if duration > 1*time.Second {
			log.Warn("数据库查询耗时过长")
		} else {
			log.Debug("数据库查询完成")
		}
	}
}

// GetDB 获取数据库连接（增强版，带日志）
func (d *Database) GetDBWithLogging() *sql.DB {
	d.logger.Debug("获取数据库连接")
	return d.db
}

// createMailboxManagementTables 创建邮箱管理相关表
func (d *Database) createMailboxManagementTables() error {
	// 批量操作记录表
	batchOperationsSQL := `
	CREATE TABLE IF NOT EXISTS batch_operations (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		operation_id TEXT UNIQUE NOT NULL,
		operation_type TEXT NOT NULL,
		status TEXT DEFAULT 'pending',
		total_count INTEGER DEFAULT 0,
		processed_count INTEGER DEFAULT 0,
		success_count INTEGER DEFAULT 0,
		failed_count INTEGER DEFAULT 0,
		error_message TEXT,
		created_by TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		started_at DATETIME,
		completed_at DATETIME,
		progress_data TEXT,
		operation_params TEXT
	);`

	if _, err := d.db.Exec(batchOperationsSQL); err != nil {
		return fmt.Errorf("创建批量操作表失败: %w", err)
	}

	// 邮箱验证任务表
	verificationTasksSQL := `
	CREATE TABLE IF NOT EXISTS mailbox_verification_tasks (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		task_id TEXT UNIQUE NOT NULL,
		batch_operation_id TEXT,
		account_email TEXT NOT NULL,
		status TEXT DEFAULT 'pending',
		verification_type TEXT DEFAULT 'login',
		started_at DATETIME,
		completed_at DATETIME,
		duration_ms INTEGER,
		error_message TEXT,
		verification_result TEXT,
		retry_count INTEGER DEFAULT 0,
		max_retries INTEGER DEFAULT 3,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	if _, err := d.db.Exec(verificationTasksSQL); err != nil {
		return fmt.Errorf("创建验证任务表失败: %w", err)
	}

	// 任务调度状态表
	schedulerStatusSQL := `
	CREATE TABLE IF NOT EXISTS task_scheduler_status (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		task_name TEXT UNIQUE NOT NULL,
		task_type TEXT NOT NULL,
		status TEXT DEFAULT 'stopped',
		current_operation_id TEXT,
		last_run_at DATETIME,
		next_run_at DATETIME,
		run_count INTEGER DEFAULT 0,
		error_count INTEGER DEFAULT 0,
		last_error TEXT,
		config_data TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	if _, err := d.db.Exec(schedulerStatusSQL); err != nil {
		return fmt.Errorf("创建调度状态表失败: %w", err)
	}

	// 邮箱统计表
	statisticsSQL := `
	CREATE TABLE IF NOT EXISTS mailbox_statistics (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		stat_date DATE NOT NULL,
		total_accounts INTEGER DEFAULT 0,
		active_accounts INTEGER DEFAULT 0,
		verified_accounts INTEGER DEFAULT 0,
		failed_accounts INTEGER DEFAULT 0,
		disabled_accounts INTEGER DEFAULT 0,
		new_imports_today INTEGER DEFAULT 0,
		verification_success_rate REAL DEFAULT 0.0,
		avg_verification_time_ms INTEGER DEFAULT 0,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(stat_date)
	);`

	if _, err := d.db.Exec(statisticsSQL); err != nil {
		return fmt.Errorf("创建统计表失败: %w", err)
	}

	// 任务日志表 - 用于记录详细的任务执行日志
	taskLogsSQL := `
	CREATE TABLE IF NOT EXISTS task_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		task_id TEXT UNIQUE NOT NULL,
		operation_type TEXT NOT NULL,
		email TEXT NOT NULL,
		status TEXT NOT NULL,
		start_time DATETIME NOT NULL,
		end_time DATETIME,
		duration_ms INTEGER,
		error_message TEXT,
		batch_id TEXT,
		detail_log_path TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	if _, err := d.db.Exec(taskLogsSQL); err != nil {
		return fmt.Errorf("创建任务日志表失败: %w", err)
	}

	// 创建任务日志表的索引
	taskLogIndexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_task_logs_email ON task_logs(email)",
		"CREATE INDEX IF NOT EXISTS idx_task_logs_operation_type ON task_logs(operation_type)",
		"CREATE INDEX IF NOT EXISTS idx_task_logs_status ON task_logs(status)",
		"CREATE INDEX IF NOT EXISTS idx_task_logs_start_time ON task_logs(start_time)",
		"CREATE INDEX IF NOT EXISTS idx_task_logs_batch_id ON task_logs(batch_id)",
	}

	for _, indexSQL := range taskLogIndexes {
		if _, err := d.db.Exec(indexSQL); err != nil {
			return fmt.Errorf("创建任务日志索引失败: %w", err)
		}
	}

	// 插入默认任务调度器数据
	return d.insertDefaultSchedulerData()
}

// insertDefaultSchedulerData 插入默认的任务调度器数据
func (d *Database) insertDefaultSchedulerData() error {
	defaultTasks := []string{
		`INSERT OR IGNORE INTO task_scheduler_status (task_name, task_type, status, config_data) VALUES
		('batch_mailbox_verification', 'batch_verification', 'stopped', '{"concurrent_limit": 10, "retry_limit": 3, "timeout_seconds": 30}')`,

		`INSERT OR IGNORE INTO task_scheduler_status (task_name, task_type, status, config_data) VALUES
		('mailbox_health_monitor', 'health_check', 'stopped', '{"check_interval_minutes": 60, "batch_size": 50}')`,

		`INSERT OR IGNORE INTO task_scheduler_status (task_name, task_type, status, config_data) VALUES
		('failed_account_retry', 'cleanup', 'stopped', '{"retry_interval_hours": 24, "max_retry_attempts": 5}')`,
	}

	for _, sql := range defaultTasks {
		if _, err := d.db.Exec(sql); err != nil {
			return fmt.Errorf("插入默认任务数据失败: %w", err)
		}
	}

	return nil
}
