package pool

import (
	"fmt"
	"go-mail/internal/errors"
	"go-mail/internal/types"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
)

// SessionPool 会话池实现
type SessionPool struct {
	sessions map[string]*types.Session
	mutex    sync.RWMutex
	timeout  time.Duration
}

// NewSessionPool 创建新的会话池
func NewSessionPool(timeout time.Duration) *SessionPool {
	return &SessionPool{
		sessions: make(map[string]*types.Session),
		timeout:  timeout,
	}
}

// Create 创建新会话
func (p *SessionPool) Create(account types.Account, proxy *types.ProxyConfig) (*types.Session, error) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// 检查是否已存在该账户的活跃会话
	for _, session := range p.sessions {
		if session.Account.Username == account.Username &&
			session.Status == types.SessionStatusActive {
			return nil, errors.NewSessionError(errors.ErrCodeSessionConflict,
				fmt.Sprintf("账户 %s 已有活跃会话", account.Username), nil)
		}
	}

	// 创建新会话
	session := &types.Session{
		ID:        uuid.New().String(),
		Account:   account,
		Proxy:     proxy,
		CreatedAt: time.Now(),
		LastUsed:  time.Now(),
		Status:    types.SessionStatusConnecting,
	}

	// 添加到会话池
	p.sessions[session.ID] = session

	return session, nil
}

// Get 获取会话
func (p *SessionPool) Get(sessionID string) (*types.Session, error) {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	session, exists := p.sessions[sessionID]
	if !exists {
		return nil, errors.NewSessionError(errors.ErrCodeSessionNotFound,
			fmt.Sprintf("会话 %s 不存在", sessionID), nil)
	}

	// 检查会话是否过期
	if p.isSessionExpired(session) {
		return nil, errors.NewSessionError(errors.ErrCodeSessionExpired,
			fmt.Sprintf("会话 %s 已过期", sessionID), nil)
	}

	// 返回副本以避免外部修改
	sessionCopy := *session
	return &sessionCopy, nil
}

// Remove 移除会话
func (p *SessionPool) Remove(sessionID string) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if _, exists := p.sessions[sessionID]; !exists {
		return errors.NewSessionError(errors.ErrCodeSessionNotFound,
			fmt.Sprintf("会话 %s 不存在", sessionID), nil)
	}

	delete(p.sessions, sessionID)
	return nil
}

// GetActive 获取所有活跃会话
func (p *SessionPool) GetActive() []types.Session {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	var active []types.Session
	for _, session := range p.sessions {
		if session.Status == types.SessionStatusActive && !p.isSessionExpired(session) {
			active = append(active, *session)
		}
	}

	return active
}

// GetByAccount 根据账户获取会话
func (p *SessionPool) GetByAccount(username string) (*types.Session, error) {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	for _, session := range p.sessions {
		if session.Account.Username == username &&
			session.Status == types.SessionStatusActive &&
			!p.isSessionExpired(session) {
			sessionCopy := *session
			return &sessionCopy, nil
		}
	}

	return nil, errors.NewSessionError(errors.ErrCodeSessionNotFound,
		fmt.Sprintf("账户 %s 没有活跃会话", username), nil)
}

// Cleanup 清理过期会话
func (p *SessionPool) Cleanup() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	var expiredSessions []string
	for sessionID, session := range p.sessions {
		if p.isSessionExpired(session) {
			expiredSessions = append(expiredSessions, sessionID)
		}
	}

	// 移除过期会话
	for _, sessionID := range expiredSessions {
		delete(p.sessions, sessionID)
	}

	return nil
}

// Count 获取会话总数
func (p *SessionPool) Count() int {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	return len(p.sessions)
}

// Clear 清空会话池
func (p *SessionPool) Clear() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.sessions = make(map[string]*types.Session)
	return nil
}

// UpdateSession 更新会话信息
func (p *SessionPool) UpdateSession(sessionID string, jsessionID, navigatorSID, finalURL string) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	session, exists := p.sessions[sessionID]
	if !exists {
		return errors.NewSessionError(errors.ErrCodeSessionNotFound,
			fmt.Sprintf("会话 %s 不存在", sessionID), nil)
	}

	session.JSessionID = jsessionID
	session.NavigatorSID = navigatorSID
	session.FinalURL = finalURL
	session.Status = types.SessionStatusActive
	session.LastUsed = time.Now()

	return nil
}

// SetClient 设置会话的HTTP客户端
func (p *SessionPool) SetClient(sessionID string, client *http.Client) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	session, exists := p.sessions[sessionID]
	if !exists {
		return errors.NewSessionError(errors.ErrCodeSessionNotFound,
			fmt.Sprintf("会话 %s 不存在", sessionID), nil)
	}

	session.Client = client
	session.LastUsed = time.Now()

	return nil
}

// UpdateLastUsed 更新会话最后使用时间
func (p *SessionPool) UpdateLastUsed(sessionID string) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	session, exists := p.sessions[sessionID]
	if !exists {
		return errors.NewSessionError(errors.ErrCodeSessionNotFound,
			fmt.Sprintf("会话 %s 不存在", sessionID), nil)
	}

	session.LastUsed = time.Now()
	return nil
}

// UpdateStatus 更新会话状态
func (p *SessionPool) UpdateStatus(sessionID string, status types.SessionStatus) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	session, exists := p.sessions[sessionID]
	if !exists {
		return errors.NewSessionError(errors.ErrCodeSessionNotFound,
			fmt.Sprintf("会话 %s 不存在", sessionID), nil)
	}

	session.Status = status
	if status == types.SessionStatusExpired || status == types.SessionStatusError {
		// 状态变为过期或错误时，不更新LastUsed
	} else {
		session.LastUsed = time.Now()
	}

	return nil
}

// isSessionExpired 检查会话是否过期
func (p *SessionPool) isSessionExpired(session *types.Session) bool {
	return time.Since(session.LastUsed) > p.timeout
}

// GetSessionsByStatus 根据状态获取会话
func (p *SessionPool) GetSessionsByStatus(status types.SessionStatus) []types.Session {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	var sessions []types.Session
	for _, session := range p.sessions {
		if session.Status == status {
			sessions = append(sessions, *session)
		}
	}

	return sessions
}

// GetStatistics 获取会话池统计信息
func (p *SessionPool) GetStatistics() map[string]interface{} {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	stats := make(map[string]interface{})
	statusCount := make(map[string]int)
	proxyCount := make(map[string]int)

	activeCount := 0
	expiredCount := 0

	for _, session := range p.sessions {
		statusCount[session.Status.String()]++

		if session.Proxy != nil {
			proxyCount[session.Proxy.ID]++
		} else {
			proxyCount["direct"]++
		}

		if session.Status == types.SessionStatusActive && !p.isSessionExpired(session) {
			activeCount++
		}

		if p.isSessionExpired(session) {
			expiredCount++
		}
	}

	stats["total_sessions"] = len(p.sessions)
	stats["active_sessions"] = activeCount
	stats["expired_sessions"] = expiredCount
	stats["status_distribution"] = statusCount
	stats["proxy_distribution"] = proxyCount

	return stats
}

// GetExpiredSessions 获取过期会话列表
func (p *SessionPool) GetExpiredSessions() []string {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	var expired []string
	for sessionID, session := range p.sessions {
		if p.isSessionExpired(session) {
			expired = append(expired, sessionID)
		}
	}

	return expired
}
