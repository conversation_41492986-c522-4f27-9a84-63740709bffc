package client

import (
	"context"
	"crypto/rand"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"go-mail/internal/errors"
	"go-mail/internal/types"
	"io"
	mathrand "math/rand"
	"net"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"strconv"
	"strings"
	"time"

	"golang.org/x/net/proxy"
)

// DeviceParams 设备参数结构体
type DeviceParams struct {
	ScreenResolution string // 屏幕分辨率，如 "1920x1080"
	ViewportSize     string // 视口大小，如 "837x570"
	UserAgent        string // 用户代理字符串
	OutputType       string // 输出类型，如 "desktop", "tablet", "mobile"
}

// LoginClient Mail.com登录客户端实现
type LoginClient struct {
	config       *types.ManagerConfig
	deviceParams *DeviceParams // 缓存的设备参数
}

// NewLoginClient 创建新的登录客户端
func NewLoginClient(config *types.ManagerConfig) *LoginClient {
	return &LoginClient{
		config: config,
	}
}

// LoginLogger 登录日志记录器接口
type LoginLogger interface {
	LogInitialRequest(account types.Account, proxyConfig *types.ProxyConfig) error
	LogOTTRequest(duration time.Duration, ott string) error
	LogRedirectRequest(duration time.Duration, redirectURL string) error
	LogSessionRequest(duration time.Duration, sid string) error
	LogFinalSession(duration time.Duration, jsessionID, navigatorSID, finalURL string) error
	LogError(stepName, details, errorMessage string) error
	LogSuccess(loginResult *types.LoginResult) error
	LogFailure(errorMessage string) error
}

// Login 执行登录流程
func (c *LoginClient) Login(ctx context.Context, account types.Account, proxyConfig *types.ProxyConfig) (*types.LoginResult, error) {
	return c.LoginWithLogger(ctx, account, proxyConfig, nil)
}

// LoginWithLogger 执行带日志记录的登录流程
func (c *LoginClient) LoginWithLogger(ctx context.Context, account types.Account, proxyConfig *types.ProxyConfig, logger LoginLogger) (*types.LoginResult, error) {
	// 创建HTTP客户端
	client, err := c.createHTTPClient(proxyConfig)
	if err != nil {
		return nil, errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建HTTP客户端失败", err)
	}

	// 执行登录流程
	result := &types.LoginResult{
		LoginTime: time.Now(),
	}

	// 记录初始请求
	if logger != nil {
		if err := logger.LogInitialRequest(account, proxyConfig); err != nil {
			fmt.Printf("记录初始请求失败: %v\n", err)
		}
	}

	// 步骤1: 初始登录请求
	start := time.Now()
	ott, err := c.performInitialLogin(ctx, client, account)
	if err != nil {
		result.Success = false
		result.Message = err.Error()
		if logger != nil {
			logger.LogError("initial_login", "初始登录请求失败", err.Error())
			logger.LogFailure(err.Error())
		}
		return result, err
	}
	if logger != nil {
		logger.LogOTTRequest(time.Since(start), ott)
	}

	// 步骤2: 获取跳转令牌
	start = time.Now()
	redirectURL, err := c.getRedirectToken(ctx, client, ott)
	if err != nil {
		result.Success = false
		result.Message = err.Error()
		if logger != nil {
			logger.LogError("get_redirect_token", "获取跳转令牌失败", err.Error())
			logger.LogFailure(err.Error())
		}
		return result, err
	}
	if logger != nil {
		logger.LogRedirectRequest(time.Since(start), redirectURL)
	}

	// 步骤3: 获取会话ID
	start = time.Now()
	sid, err := c.getSessionID(ctx, client, redirectURL)
	if err != nil {
		result.Success = false
		result.Message = err.Error()
		if logger != nil {
			logger.LogError("get_session_id", "获取会话ID失败", err.Error())
			logger.LogFailure(err.Error())
		}
		return result, err
	}
	if logger != nil {
		logger.LogSessionRequest(time.Since(start), sid)
	}

	// 步骤4: 获取最终会话
	start = time.Now()
	jsessionID, navigatorSID, finalURL, err := c.getFinalSession(ctx, client, sid)
	if err != nil {
		result.Success = false
		result.Message = err.Error()
		if logger != nil {
			logger.LogError("get_final_session", "获取最终会话失败", err.Error())
			logger.LogFailure(err.Error())
		}
		return result, err
	}
	if logger != nil {
		logger.LogFinalSession(time.Since(start), jsessionID, navigatorSID, finalURL)
	}

	// 构建成功结果
	result.Success = true
	result.JSessionID = jsessionID
	result.NavigatorSID = navigatorSID // 修复：使用正确的Navigator SID
	result.RedirectURL = finalURL
	result.Message = "登录成功"

	// 提取cookies并添加调试信息
	result.Cookies = c.extractCookies(client)

	// 调试：输出提取到的cookies信息
	c.debugExtractedCookies(result.Cookies)

	// 记录成功
	if logger != nil {
		if err := logger.LogSuccess(result); err != nil {
			fmt.Printf("记录登录成功失败: %v\n", err)
		}
	}

	return result, nil
}

// CreateHTTPClient 创建配置好的HTTP客户端（公开方法）
func (c *LoginClient) CreateHTTPClient(proxyConfig *types.ProxyConfig) (*http.Client, error) {
	return c.createHTTPClient(proxyConfig)
}

// {{ AURA-X: Add - 创建带有指定cookies的HTTP客户端用于会话. Approval: 寸止(ID:1737364800). }}
// CreateHTTPClientWithCookies 创建配置好的HTTP客户端并设置指定的cookies
func (c *LoginClient) CreateHTTPClientWithCookies(proxyConfig *types.ProxyConfig, cookies []*http.Cookie) (*http.Client, error) {
	// 创建基础HTTP客户端
	client, err := c.createHTTPClient(proxyConfig)
	if err != nil {
		return nil, err
	}

	// 设置cookies到客户端
	if len(cookies) > 0 {
		//fmt.Printf("\n=== 调试：为会话客户端设置 %d 个cookies ===\n", len(cookies))
		c.setCookiesToClient(client, cookies)

		// 验证cookies是否正确设置
		c.debugSessionClientCookies(client)
	} else {
		fmt.Printf("\n=== 警告：没有cookies需要设置到会话客户端 ===\n")
	}

	return client, nil
}

// createHTTPClient 创建配置好的HTTP客户端
func (c *LoginClient) createHTTPClient(proxyConfig *types.ProxyConfig) (*http.Client, error) {
	// 创建Cookie Jar
	jar, err := cookiejar.New(nil)
	if err != nil {
		return nil, fmt.Errorf("创建cookie jar失败: %w", err)
	}

	// 创建传输层
	transport := &http.Transport{
		DialContext: (&net.Dialer{
			Timeout:   10 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
		},
		MaxIdleConns:        100,
		IdleConnTimeout:     90 * time.Second,
		TLSHandshakeTimeout: 10 * time.Second,
	}

	// 配置代理
	if proxyConfig != nil && proxyConfig.Enabled {
		proxyURL, err := c.buildProxyURL(proxyConfig)
		if err != nil {
			return nil, errors.NewProxyError(errors.ErrCodeProxyConnection, "代理配置错误", err)
		}

		if proxyConfig.Type == types.ProxyTypeSOCKS5 {
			// SOCKS5代理
			dialer, err := proxy.SOCKS5("tcp", fmt.Sprintf("%s:%d", proxyConfig.Host, proxyConfig.Port),
				&proxy.Auth{User: proxyConfig.Username, Password: proxyConfig.Password}, proxy.Direct)
			if err != nil {
				return nil, errors.NewProxyError(errors.ErrCodeProxyConnection, "SOCKS5代理连接失败", err)
			}
			transport.DialContext = dialer.(proxy.ContextDialer).DialContext
		} else {
			// HTTP代理
			transport.Proxy = http.ProxyURL(proxyURL)
		}
	}

	client := &http.Client{
		Transport: transport,
		Jar:       jar,
		Timeout:   c.config.RequestTimeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 禁止自动跟随重定向，让代码手动控制重定向流程
			// 这样可以确保不丢失中间步骤的 Cookie 和响应头
			return http.ErrUseLastResponse
		},
	}

	return client, nil
}

// buildProxyURL 构建代理URL
func (c *LoginClient) buildProxyURL(proxyConfig *types.ProxyConfig) (*url.URL, error) {
	scheme := "http"
	if proxyConfig.Type == types.ProxyTypeSOCKS5 {
		scheme = "socks5"
	}

	proxyURL := &url.URL{
		Scheme: scheme,
		Host:   fmt.Sprintf("%s:%d", proxyConfig.Host, proxyConfig.Port),
	}

	if proxyConfig.Username != "" {
		proxyURL.User = url.UserPassword(proxyConfig.Username, proxyConfig.Password)
	}

	return proxyURL, nil
}

// performInitialLogin 执行初始登录请求
func (c *LoginClient) performInitialLogin(ctx context.Context, client *http.Client, account types.Account) (string, error) {
	// 步骤0: 初始化必需的 Cookie
	if err := c.initializeCookies(ctx, client); err != nil {
		return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "初始化Cookie失败", err)
	}

	// 构建登录表单数据 - 修复URL编码问题，使用单层编码而非双层编码
	// 直接构建已编码的表单字符串，避免url.Values的双重编码
	formDataStr := fmt.Sprintf(
		"ibaInfo=abd%%3Dtrue&service=mailint&statistics=2V6%%2Feba91L%%2BhmxDhwggJPx5RkuAjCHrO%%2FsJtAVq%%2Bg9gP7SHjpgK9QNUyE1fph2NPtSa7qVG8xUiKZF1PbwxDDP0w9JH4hOJ3A9A5XDzTLivSjROO7TFnjmBQNiBP%%2BSlxIPgiCHYGEBuqBEvhluCcWOXQvGmndBZnbZHZu7T0oxM82a30%%2F82vp58Qvu%%2B64hEv&uasServiceID=mc_starter_mailcom&successURL=https%%3A%%2F%%2F%%24%%28clientName%%29-%%24%%28dataCenter%%29.mail.com%%2Flogin&loginFailedURL=https%%3A%%2F%%2Fwww.mail.com%%2Flogout%%2F%%3Fls%%3Dwd&loginErrorURL=https%%3A%%2F%%2Fwww.mail.com%%2Flogout%%2F%%3Fls%%3Dte&edition=us&lang=en&usertype=standard&username=%s&password=%s",
		url.QueryEscape(account.Username),
		url.QueryEscape(account.Password),
	)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", "https://login.mail.com/login",
		strings.NewReader(formDataStr))
	if err != nil {
		return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "创建登录请求失败", err)
	}

	// 设置请求头
	c.setCommonHeaders(req)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Origin", "https://www.mail.com")
	req.Header.Set("Referer", "https://www.mail.com/")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection, "登录请求失败", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode == http.StatusUnauthorized {
		return "", errors.NewAuthenticationError(errors.ErrCodeAuthInvalidCredentials, "用户名或密码错误", nil)
	}

	if resp.StatusCode != http.StatusFound && resp.StatusCode != http.StatusSeeOther {
		return "", errors.NewNetworkError(errors.ErrCodeNetworkConnection,
			fmt.Sprintf("登录请求返回异常状态码: %d", resp.StatusCode), nil)
	}

	// 从Location头获取重定向URL并提取OTT
	location := resp.Header.Get("Location")
	if location == "" {
		return "", errors.NewSessionError(errors.ErrCodeSessionInvalid, "未获取到重定向URL", nil)
	}

	// 提取OTT参数
	ott, err := c.extractOTTFromURL(location)
	if err != nil {
		return "", errors.NewSessionError(errors.ErrCodeSessionInvalid, "提取OTT失败", err)
	}

	return ott, nil
}

// extractOTTFromURL 从URL中提取OTT参数
func (c *LoginClient) extractOTTFromURL(urlStr string) (string, error) {
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return "", fmt.Errorf("解析URL失败: %w", err)
	}

	ott := parsedURL.Query().Get("ott")
	if ott == "" {
		return "", fmt.Errorf("URL中未找到OTT参数")
	}

	return ott, nil
}

// setCommonHeaders 设置通用请求头
func (c *LoginClient) setCommonHeaders(req *http.Request) {
	req.Header.Set("User-Agent", c.config.UserAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd")
	req.Header.Set("Cache-Control", "max-age=0")
	req.Header.Set("Sec-Fetch-Site", "same-site")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Ch-Ua", `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"Windows"`)
	req.Header.Set("Upgrade-Insecure-Requests", "1")
}

// initializeCookies 初始化登录所需的 Cookie
func (c *LoginClient) initializeCookies(ctx context.Context, client *http.Client) error {
	// 1. 设置基础固定 Cookie（GDNA、uiconsent 等）
	if err := c.setFixedCookies(client); err != nil {
		return fmt.Errorf("设置固定Cookie失败: %w", err)
	}

	// 2. 获取 _autuserid2 Cookie
	if err := c.fetchAutUserIdCookie(ctx, client); err != nil {
		return fmt.Errorf("获取_autuserid2 Cookie失败: %w", err)
	}

	// 3. 获取 wa Cookie（需要 GDNA 和 uiconsent Cookie 支持）
	if err := c.fetchWaCookie(ctx, client); err != nil {
		return fmt.Errorf("获取wa Cookie失败: %w", err)
	}

	// 4. 生成并设置 adtrgtng Cookie（在最后设置，用于登录请求）
	if err := c.generateAdtrgtngCookie(client); err != nil {
		return fmt.Errorf("生成adtrgtng Cookie失败: %w", err)
	}

	// 5. 调试输出所有 Cookie（可选）
	c.debugCookies(client, "https://www.mail.com")
	// c.debugCookies(client, "https://wa.mail.com")
	// c.debugCookies(client, "https://united-infos.net")

	return nil
}

// generateAdtrgtngCookie 生成并设置 adtrgtng Cookie
func (c *LoginClient) generateAdtrgtngCookie(client *http.Client) error {
	// 构建 JSON 数据
	data := map[string]interface{}{
		"a": map[string]interface{}{
			"cs": 1,
			"ca": 1,
			"i":  1,
			"a":  1,
			"ui": 0,
			"fp": 0,
		},
	}

	// 转换为 JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("JSON编码失败: %w", err)
	}

	// Base64 编码
	encodedData := base64.StdEncoding.EncodeToString(jsonData)

	// 创建 Cookie 并添加到客户端
	cookie := &http.Cookie{
		Name:   "adtrgtng",
		Value:  encodedData,
		Domain: ".mail.com",
		Path:   "/",
	}

	// 添加到 CookieJar
	u, _ := url.Parse("https://www.mail.com")
	client.Jar.SetCookies(u, []*http.Cookie{cookie})

	return nil
}

// fetchAutUserIdCookie 获取 _autuserid2 Cookie
func (c *LoginClient) fetchAutUserIdCookie(ctx context.Context, client *http.Client) error {
	// 生成时间戳
	timestamp := time.Now().UnixMilli()
	reqURL := fmt.Sprintf("https://united-infos.net/i?raw=1&ts=%d", timestamp)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", c.config.UserAgent)
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Origin", "https://www.mail.com")
	req.Header.Set("Referer", "https://www.mail.com/")
	req.Header.Set("Sec-Fetch-Site", "cross-site")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Storage-Access", "active")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Set("Sec-Ch-Ua", `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"Windows"`)

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("请求返回异常状态码: %d", resp.StatusCode)
	}

	// 读取响应体获取 UserID 值
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应体失败: %w", err)
	}

	userID := strings.TrimSpace(string(body))
	if userID == "" || userID == "0" {
		return fmt.Errorf("响应体中未获取到有效的UserID: %s", userID)
	}

	// 验证 UserID 是否为有效数字
	if _, err := strconv.ParseInt(userID, 10, 64); err != nil {
		return fmt.Errorf("UserID 不是有效数字: %s", userID)
	}

	// 手动创建并设置 _autuserid2 Cookie 到 .mail.com 域
	autUserIdCookie := &http.Cookie{
		Name:   "_autuserid2",
		Value:  userID,
		Domain: ".mail.com",
		Path:   "/",
	}

	// 添加到 CookieJar
	mailURL, _ := url.Parse("https://www.mail.com")
	client.Jar.SetCookies(mailURL, []*http.Cookie{autUserIdCookie})

	// 验证是否成功获取和设置了相关 Cookie
	if err := c.validateAutUserIdCookie(resp, client, userID); err != nil {
		return fmt.Errorf("验证_autuserid2 Cookie失败: %w", err)
	}

	return nil
}

// fetchWaCookie 获取 wa Cookie
func (c *LoginClient) fetchWaCookie(ctx context.Context, client *http.Client) error {
	// 生成时间戳
	timestamp := time.Now().UnixMilli()

	// 获取随机化的设备参数
	deviceParams := c.getDeviceParams()

	// 构建查询参数
	params := url.Values{
		"homepage.default.pi.homepage.index": {""},
		"category":                           {"homepage"},
		"wa_c_ti":                            {"mail.com–homeoffreeemailservices,webmailan"},
		"wa_c_id":                            {"7518"},
		"wa_p_pn":                            {"AP"},
		"wa_sc_2":                            {"default"},
		"wa_sc_5":                            {"index"},
		"country":                            {"us"},
		"kid_0":                              {"kid@<EMAIL>"},
		"wa_p_sr":                            {deviceParams.ScreenResolution}, // 使用随机化的屏幕分辨率
		"wa_p_vp":                            {deviceParams.ViewportSize},     // 使用随机化的视口大小
		"wa_output":                          {deviceParams.OutputType},       // 使用动态的输出类型
		"wa_t":                               {"20250700"},
		"adblock":                            {"0"},
		"ns__t":                              {fmt.Sprintf("%d", timestamp)},
		"ns_c":                               {"UTF-8"},
		"ns_jspageurl":                       {"https://www.mail.com/"},
		"ns_referrer":                        {"null"},
	}

	reqURL := "https://wa.mail.com/1and1/mailcom/s?" + params.Encode()

	// 调试：检查发送请求前的 Cookie 状态
	c.debugCookies(client, "https://www.mail.com")
	c.debugCookies(client, "https://wa.mail.com")

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", c.config.UserAgent)
	req.Header.Set("Accept", "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8")
	req.Header.Set("Referer", "https://www.mail.com/")
	req.Header.Set("Sec-Fetch-Site", "same-site")
	req.Header.Set("Sec-Fetch-Mode", "no-cors")
	req.Header.Set("Sec-Fetch-Dest", "image")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Set("Sec-Ch-Ua", `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"Windows"`)
	req.Header.Set("Priority", "i")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态 (期望 302 重定向)
	if resp.StatusCode != http.StatusFound {
		return fmt.Errorf("请求返回异常状态码: %d", resp.StatusCode)
	}

	// 验证是否成功获取到 wa Cookie
	if err := c.validateWaCookie(resp, client); err != nil {
		return fmt.Errorf("验证wa Cookie失败: %w", err)
	}

	return nil
}

// setFixedCookies 设置其他固定 Cookie
func (c *LoginClient) setFixedCookies(client *http.Client) error {
	// 生成时间戳
	timestamp := time.Now().Unix()

	// 生成随机 UUID (简化版)
	uuid := c.generateSimpleUUID()

	// 生成随机的 GA 客户端ID
	gaClientID := c.generateGAClientID()

	// 定义固定 Cookie
	cookies := []*http.Cookie{
		{
			Name:   "__Host-ls.rec",
			Value:  uuid,
			Domain: "",
			Path:   "/",
			Secure: true,
		},
		{
			Name:   "GDNA",
			Value:  "true",
			Domain: ".mail.com",
			Path:   "/",
		},
		{
			Name:   "uiconsent",
			Value:  `{%22permissionFeature%22:[%22fullConsent%22]}`,
			Domain: ".mail.com",
			Path:   "/",
		},
		{
			Name:   "cookieKID",
			Value:  "kid%40autoref%40mail.com", // 修复：使用URL编码格式
			Domain: ".mail.com",
			Path:   "/",
		},
		{
			Name:   "cookiePartner",
			Value:  "kid%40autoref%40mail.com", // 修复：使用URL编码格式
			Domain: ".mail.com",
			Path:   "/",
		},
		{
			Name:   "_ga",
			Value:  fmt.Sprintf("GA1.1.%s.%d", gaClientID, timestamp),
			Domain: ".mail.com",
			Path:   "/",
		},
		{
			Name:   "_ga_V2FMR8VFB6",
			Value:  fmt.Sprintf("GS2.1.s%d$o1$g0$t%d$j53$l0$h0", timestamp, timestamp+7),
			Domain: ".mail.com",
			Path:   "/",
		},
	}

	// 添加所有 Cookie 到 CookieJar
	u, _ := url.Parse("https://www.mail.com")
	client.Jar.SetCookies(u, cookies)

	// 为 wa.mail.com 域单独设置关键 Cookie，确保能够正确发送
	waCookies := []*http.Cookie{
		{
			Name:   "GDNA",
			Value:  "true",
			Domain: ".mail.com",
			Path:   "/",
		},
		{
			Name:   "uiconsent",
			Value:  `{%22permissionFeature%22:[%22fullConsent%22]}`,
			Domain: ".mail.com",
			Path:   "/",
		},
	}

	waURL, _ := url.Parse("https://wa.mail.com")
	client.Jar.SetCookies(waURL, waCookies)

	return nil
}

// generateSimpleUUID 生成简单的 UUID
func (c *LoginClient) generateSimpleUUID() string {
	// 生成简单的 UUID 格式字符串
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x-%x-%x-%x-%x", b[0:4], b[4:6], b[6:8], b[8:10], b[10:16])
}

// generateDeviceParams 生成随机化的设备参数
func (c *LoginClient) generateDeviceParams() *DeviceParams {
	// 常见的屏幕分辨率列表
	resolutions := []struct {
		width  int
		height int
	}{
		{1920, 1080}, // Full HD
		{1366, 768},  // HD
		{1440, 900},  // WXGA+
		{1536, 864},  // 常见笔记本分辨率
		{1600, 900},  // HD+
		{1680, 1050}, // WSXGA+
		{1280, 720},  // HD
		{1280, 800},  // WXGA
		{1280, 1024}, // SXGA
		{1024, 768},  // XGA
		{2560, 1440}, // QHD
		{3840, 2160}, // 4K UHD
	}

	// 使用当前时间作为随机种子
	source := mathrand.NewSource(time.Now().UnixNano())
	rng := mathrand.New(source)

	// 随机选择屏幕分辨率
	resolution := resolutions[rng.Intn(len(resolutions))]
	screenRes := fmt.Sprintf("%dx%d", resolution.width, resolution.height)

	// 计算合理的视口大小（通常比屏幕分辨率小）
	// 减去浏览器UI占用的空间（工具栏、地址栏等）
	viewportWidth := resolution.width - rng.Intn(100) - 50    // 减去50-150像素
	viewportHeight := resolution.height - rng.Intn(200) - 100 // 减去100-300像素

	// 确保最小尺寸
	if viewportWidth < 800 {
		viewportWidth = 800
	}
	if viewportHeight < 600 {
		viewportHeight = 600
	}

	viewportSize := fmt.Sprintf("%dx%d", viewportWidth, viewportHeight)

	// 根据屏幕分辨率确定输出类型
	var outputType string
	if resolution.width >= 1920 {
		outputType = "desktop"
	} else if resolution.width >= 1024 {
		outputType = "tablet"
	} else {
		outputType = "mobile"
	}

	return &DeviceParams{
		ScreenResolution: screenRes,
		ViewportSize:     viewportSize,
		UserAgent:        c.config.UserAgent, // 使用配置中的 UserAgent
		OutputType:       outputType,
	}
}

// getDeviceParams 获取设备参数（带缓存）
func (c *LoginClient) getDeviceParams() *DeviceParams {
	if c.deviceParams == nil {
		c.deviceParams = c.generateDeviceParams()
	}
	return c.deviceParams
}

// generateGAClientID 生成随机的 Google Analytics 客户端ID
func (c *LoginClient) generateGAClientID() string {
	// GA 客户端ID通常是一个10位数字
	source := mathrand.NewSource(time.Now().UnixNano())
	rng := mathrand.New(source)

	// 生成1000000000到9999999999之间的随机数
	clientID := rng.Int63n(9000000000) + 1000000000
	return fmt.Sprintf("%d", clientID)
}

// validateWaCookie 验证是否成功获取到 wa Cookie
func (c *LoginClient) validateWaCookie(resp *http.Response, client *http.Client) error {
	// 检查响应头中的 Set-Cookie
	setCookies := resp.Header.Values("Set-Cookie")
	waFound := false

	for _, cookie := range setCookies {
		if strings.HasPrefix(cookie, "wa=") {
			waFound = true
			break
		}
	}

	if !waFound {
		return fmt.Errorf("响应中未找到wa Cookie")
	}

	// 验证 CookieJar 中是否已正确存储 wa Cookie
	u, _ := url.Parse("https://wa.mail.com")
	cookies := client.Jar.Cookies(u)

	for _, cookie := range cookies {
		if cookie.Name == "wa" && cookie.Value != "" {
			return nil // 找到有效的 wa Cookie
		}
	}

	return fmt.Errorf("wa Cookie 未正确存储到 CookieJar")
}

// validateAutUserIdCookie 验证是否成功获取到 _autuserid2 Cookie
func (c *LoginClient) validateAutUserIdCookie(resp *http.Response, client *http.Client, expectedUserID string) error {
	// 1. 检查响应头中的 Set-Cookie 是否包含 UserID1
	setCookies := resp.Header.Values("Set-Cookie")
	userID1Found := false

	for _, cookie := range setCookies {
		if strings.Contains(cookie, "UserID1=") {
			userID1Found = true
			break
		}
	}

	if !userID1Found {
		return fmt.Errorf("响应中未找到UserID1 Cookie")
	}

	//  验证 .mail.com 域下的 _autuserid2 Cookie
	mailURL, _ := url.Parse("https://www.mail.com")
	mailCookies := client.Jar.Cookies(mailURL)

	autUserIdValid := false
	for _, cookie := range mailCookies {
		if cookie.Name == "_autuserid2" && cookie.Value == expectedUserID {
			autUserIdValid = true
			break
		}
	}

	if !autUserIdValid {
		return fmt.Errorf("_autuserid2 Cookie 未正确存储到 .mail.com 域")
	}

	return nil
}

// debugCookies 调试输出当前 CookieJar 中的所有 Cookie（仅在开发时使用）
func (c *LoginClient) debugCookies(client *http.Client, domain string) {
	/*
		 	// 可以通过环境变量或其他方式控制是否启用调试
			u, _ := url.Parse(domain)
			cookies := client.Jar.Cookies(u)

			fmt.Printf("=== Debug: Cookies for %s ===\n", domain)
			for _, cookie := range cookies {
				fmt.Printf("Cookie: %s=%s (Domain: %s, Path: %s)\n",
					cookie.Name, cookie.Value, cookie.Domain, cookie.Path)
			}
			fmt.Printf("=== End Debug ===\n")
	*/
}

// extractCookies 提取HTTP客户端中的所有cookies
func (c *LoginClient) extractCookies(client *http.Client) []*http.Cookie {
	var allCookies []*http.Cookie

	// 定义需要提取cookies的域名列表
	domains := []string{
		"https://www.mail.com",
		"https://wa.mail.com",
		"https://navigator-lxa.mail.com",
		"https://3c-lxa.mail.com",
		"https://login.mail.com",
		"https://united-infos.net",
	}

	// 从每个域名提取cookies
	for _, domain := range domains {
		if u, err := url.Parse(domain); err == nil {
			cookies := client.Jar.Cookies(u)
			allCookies = append(allCookies, cookies...)
		}
	}

	return allCookies
}

// setCookiesToClient 将cookies设置到HTTP客户端的cookie jar中
func (c *LoginClient) setCookiesToClient(client *http.Client, cookies []*http.Cookie) {
	//fmt.Printf("调试：开始设置 %d 个cookies到客户端\n", len(cookies))

	// 定义目标域名列表，用于为没有Domain信息的cookies分配域名
	targetDomains := []string{
		"https://www.mail.com",
		"https://wa.mail.com",
		"https://navigator-lxa.mail.com",
		"https://3c-lxa.mail.com",
		"https://login.mail.com",
		"https://united-infos.net",
	}

	// 为每个目标域名设置所有cookies
	for _, targetDomain := range targetDomains {
		if u, err := url.Parse(targetDomain); err == nil {
			// 创建cookies副本并设置正确的域名
			var domainCookies []*http.Cookie
			for _, cookie := range cookies {
				// 创建cookie副本
				newCookie := &http.Cookie{
					Name:     cookie.Name,
					Value:    cookie.Value,
					Path:     "/",
					Domain:   "." + u.Host, // 设置为目标域名
					Secure:   true,
					HttpOnly: false,
				}
				domainCookies = append(domainCookies, newCookie)
			}

			// 设置cookies到目标域名
			client.Jar.SetCookies(u, domainCookies)
			//fmt.Printf("调试：为域名 %s 设置了 %d 个cookies\n", targetDomain, len(domainCookies))
		}
	}
}

// debugExtractedCookies 调试输出提取到的cookies信息
func (c *LoginClient) debugExtractedCookies(cookies []*http.Cookie) {
	/* fmt.Printf("\n=== 调试：提取到的Cookies信息 ===\n")
	fmt.Printf("总共提取到 %d 个cookies:\n", len(cookies))

	// 定义关键的认证cookies列表
	keyCookies := map[string]bool{
		"tp_id":         false,
		"tpid_sec":      false,
		"iac_token":     false,
		"utag_main":     false,
		"GDNA":          false,
		"uiconsent":     false,
		"wa":            false,
		"_autuserid2":   false,
		"adtrgtng":      false,
		"_ga":           false,
		"cookieKID":     false,
		"cookiePartner": false,
	}

	// 检查每个cookie
	for _, cookie := range cookies {
		fmt.Printf("Cookie: %s=%s (Domain: %s, Path: %s)\n",
			cookie.Name, cookie.Value, cookie.Domain, cookie.Path)

		// 标记找到的关键cookies
		if _, exists := keyCookies[cookie.Name]; exists {
			keyCookies[cookie.Name] = true
		}

		// 检查是否是会话标识符cookie（以2Q7NMZu76R开头）
		if strings.HasPrefix(cookie.Name, "2Q7NMZu76R") {
			fmt.Printf("  ✓ 找到会话标识符cookie: %s\n", cookie.Name)
		}
	}

	// 输出关键cookies的检查结果
	fmt.Printf("\n=== 关键Cookies检查结果 ===\n")
	for cookieName, found := range keyCookies {
		status := "❌ 缺失"
		if found {
			status = "✓ 已找到"
		}
		fmt.Printf("%s: %s\n", cookieName, status)
	}
	fmt.Printf("=== 调试信息结束 ===\n\n") */
}

// debugSessionClientCookies 调试输出会话客户端中的cookies信息
func (c *LoginClient) debugSessionClientCookies(client *http.Client) {
	/* fmt.Printf("\n=== 调试：会话客户端Cookies验证 ===\n")

	// 检查各个域名下的cookies
	domains := []string{
		"https://www.mail.com",
		"https://wa.mail.com",
		"https://navigator-lxa.mail.com",
		"https://3c-lxa.mail.com",
		"https://login.mail.com",
		"https://united-infos.net",
	}

	totalCookies := 0
	for _, domain := range domains {
		if u, err := url.Parse(domain); err == nil {
			cookies := client.Jar.Cookies(u)
			if len(cookies) > 0 {
				fmt.Printf("\n域名 %s 下的cookies (%d个):\n", domain, len(cookies))
				for _, cookie := range cookies {
					fmt.Printf("  %s=%s\n", cookie.Name, cookie.Value)
				}
				totalCookies += len(cookies)
			}
		}
	}

	fmt.Printf("\n会话客户端总共有 %d 个cookies\n", totalCookies)
	fmt.Printf("=== 会话客户端调试结束 ===\n\n") */
}
