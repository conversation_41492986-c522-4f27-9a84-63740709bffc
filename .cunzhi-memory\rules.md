# 开发规范和规则


**项目结构参考：**
- `go-mail-client/` - 客户端代码
- `go-mail-backend/` - 后台管理系统
- `mail-frontend/` - 后台前端系统
**参考文件：**
- `@e:\ProjectCode\GoCode\go-mail/go-mail-backend\internal\api\server.go` - API路由管理
- `@e:\ProjectCode\GoCode\go-mail/go-mail-backend\cmd\single-login/` - 单账号测试流程（包含登录、取邮件列表、取邮件内容、别名操作等完整过程）
---


- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译，不要运行（用户自己运行）

- 邮箱验证问题已全面修复：1)在main.go中添加mailManager.Start(ctx)启动调用 2)修复数据库连接重复问题，使用共享连接 3)增强错误处理和组件验证 4)优化资源清理逻辑。核心错误CFG_001"管理器未启动"已解决。
