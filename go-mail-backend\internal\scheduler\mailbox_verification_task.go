package scheduler

import (
	"context"
	"fmt"
	"go-mail/internal/database"
	"go-mail/internal/manager"
	"go-mail/internal/services"
	"log/slog"
	"time"
)

// MailboxVerificationTask 邮箱验证定时任务
type MailboxVerificationTask struct {
	name           string
	interval       time.Duration
	enabled        bool
	logger         *slog.Logger
	mailManager    *manager.MailManager
	db             *database.Database
	mailboxService *services.MailboxManagementService
	lastRun        time.Time
	isRunning      bool
	config         *VerificationTaskConfig
}

// VerificationTaskConfig 验证任务配置
type VerificationTaskConfig struct {
	ConcurrentLimit    int      `json:"concurrent_limit"`
	RetryLimit         int      `json:"retry_limit"`
	TimeoutSeconds     int      `json:"timeout_seconds"`
	BatchSize          int      `json:"batch_size"`
	VerificationTypes  []string `json:"verification_types"`
	SkipRecentVerified bool     `json:"skip_recent_verified"`
	RecentHours        int      `json:"recent_hours"`
	MaxFailureRate     float64  `json:"max_failure_rate"`
	PauseOnHighFailure bool     `json:"pause_on_high_failure"`
}

// NewMailboxVerificationTask 创建邮箱验证定时任务
func NewMailboxVerificationTask(mailManager *manager.MailManager, db *database.Database, mailboxService *services.MailboxManagementService, logger *slog.Logger) *MailboxVerificationTask {
	config := &VerificationTaskConfig{
		ConcurrentLimit:    10,
		RetryLimit:         3,
		TimeoutSeconds:     30,
		BatchSize:          100,
		VerificationTypes:  []string{"login"},
		SkipRecentVerified: true,
		RecentHours:        24,
		MaxFailureRate:     0.8,
		PauseOnHighFailure: true,
	}

	return &MailboxVerificationTask{
		name:           "batch_mailbox_verification",
		interval:       time.Hour, // 每小时执行一次
		enabled:        false,     // 默认禁用，需要手动启用
		logger:         logger,
		mailManager:    mailManager,
		db:             db,
		mailboxService: mailboxService,
		config:         config,
	}
}

// GetName 获取任务名称
func (t *MailboxVerificationTask) GetName() string {
	return t.name
}

// GetInterval 获取执行间隔
func (t *MailboxVerificationTask) GetInterval() time.Duration {
	return t.interval
}

// IsEnabled 是否启用
func (t *MailboxVerificationTask) IsEnabled() bool {
	return t.enabled
}

// SetEnabled 设置启用状态
func (t *MailboxVerificationTask) SetEnabled(enabled bool) {
	t.enabled = enabled
	t.updateTaskStatus("enabled", enabled)
}

// Execute 执行任务
func (t *MailboxVerificationTask) Execute(ctx context.Context) error {
	if t.isRunning {
		t.logger.Warn("邮箱验证任务已在运行中，跳过本次执行")
		return nil
	}

	t.isRunning = true
	t.lastRun = time.Now()
	defer func() {
		t.isRunning = false
	}()

	t.logger.Info("开始执行邮箱验证任务")
	t.updateTaskStatus("status", "running")

	// 获取需要验证的账户
	accounts, err := t.getAccountsForVerification(ctx)
	if err != nil {
		t.logger.Error("获取待验证账户失败", "error", err)
		t.updateTaskStatus("status", "error")
		t.updateTaskStatus("last_error", err.Error())
		return err
	}

	if len(accounts) == 0 {
		t.logger.Info("没有需要验证的账户")
		t.updateTaskStatus("status", "stopped")
		return nil
	}

	t.logger.Info("找到待验证账户", "count", len(accounts))

	// 分批处理
	batchSize := t.config.BatchSize
	totalBatches := (len(accounts) + batchSize - 1) / batchSize

	var totalSuccess, totalFailed int

	for i := 0; i < totalBatches; i++ {
		start := i * batchSize
		end := start + batchSize
		if end > len(accounts) {
			end = len(accounts)
		}

		batch := accounts[start:end]
		t.logger.Info("处理验证批次", "batch", i+1, "total", totalBatches, "count", len(batch))

		// 执行批次验证
		successCount, failedCount, err := t.processBatch(ctx, batch)
		if err != nil {
			t.logger.Error("批次验证失败", "batch", i+1, "error", err)
			continue
		}

		totalSuccess += successCount
		totalFailed += failedCount

		// 检查失败率
		if t.config.PauseOnHighFailure && totalSuccess+totalFailed > 0 {
			failureRate := float64(totalFailed) / float64(totalSuccess+totalFailed)
			if failureRate > t.config.MaxFailureRate {
				t.logger.Warn("验证失败率过高，暂停任务", "failure_rate", failureRate, "max_allowed", t.config.MaxFailureRate)
				t.updateTaskStatus("status", "paused")
				t.updateTaskStatus("last_error", fmt.Sprintf("验证失败率过高: %.2f%%", failureRate*100))
				return fmt.Errorf("验证失败率过高: %.2f%%", failureRate*100)
			}
		}

		// 批次间暂停，避免过于频繁的请求
		if i < totalBatches-1 {
			time.Sleep(time.Second * 5)
		}
	}

	// 更新统计信息
	t.updateVerificationStatistics(totalSuccess, totalFailed)

	t.logger.Info("邮箱验证任务完成", "total", len(accounts), "success", totalSuccess, "failed", totalFailed)
	t.updateTaskStatus("status", "stopped")
	t.updateTaskStatus("run_count", "+1")

	return nil
}

// getAccountsForVerification 获取需要验证的账户
func (t *MailboxVerificationTask) getAccountsForVerification(ctx context.Context) ([]database.ExtendedAccount, error) {
	query := `
		SELECT id, email, password, login_status, verification_status, 
			   last_verification_time, is_disabled, created_at
		FROM accounts 
		WHERE is_disabled = 0 
		  AND (verification_status = 'unverified' OR verification_status = 'failed')
	`

	args := []interface{}{}

	// 如果启用了跳过最近验证的账户
	if t.config.SkipRecentVerified {
		query += " AND (last_verification_time IS NULL OR last_verification_time < ?)"
		recentTime := time.Now().Add(-time.Duration(t.config.RecentHours) * time.Hour)
		args = append(args, recentTime)
	}

	query += " ORDER BY created_at ASC LIMIT ?"
	args = append(args, t.config.BatchSize*5) // 获取更多账户以备选择

	rows, err := t.db.GetDB().Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询待验证账户失败: %w", err)
	}
	defer rows.Close()

	var accounts []database.ExtendedAccount
	for rows.Next() {
		var account database.ExtendedAccount
		err := rows.Scan(
			&account.ID, &account.Email, &account.Password, &account.LoginStatus,
			&account.VerificationStatus, &account.LastVerificationTime,
			&account.IsDisabled, &account.CreatedAt,
		)
		if err != nil {
			t.logger.Error("扫描账户数据失败", "error", err)
			continue
		}
		accounts = append(accounts, account)
	}

	return accounts, nil
}

// processBatch 处理验证批次
func (t *MailboxVerificationTask) processBatch(ctx context.Context, accounts []database.ExtendedAccount) (int, int, error) {
	// 创建验证请求
	emails := make([]string, len(accounts))
	for i, account := range accounts {
		emails[i] = account.Email
	}

	req := &database.VerificationTaskRequest{
		AccountEmails:    emails,
		VerificationType: "login",
		ConcurrentLimit:  t.config.ConcurrentLimit,
		RetryLimit:       t.config.RetryLimit,
	}

	// 使用邮箱管理服务执行验证
	result, err := t.mailboxService.StartVerificationTask(ctx, req, "system")
	if err != nil {
		return 0, 0, fmt.Errorf("启动验证任务失败: %w", err)
	}

	// 等待验证完成（简化处理，实际应该异步监控）
	time.Sleep(time.Duration(len(accounts)*t.config.TimeoutSeconds/t.config.ConcurrentLimit) * time.Second)

	// 检查验证结果
	operation, err := t.mailboxService.GetBatchOperationStatus(ctx, result.OperationID)
	if err != nil {
		return 0, 0, fmt.Errorf("获取验证结果失败: %w", err)
	}

	return operation.SuccessCount, operation.FailedCount, nil
}

// updateTaskStatus 更新任务状态
func (t *MailboxVerificationTask) updateTaskStatus(field string, value interface{}) {
	var query string
	var args []interface{}

	switch field {
	case "status":
		query = "UPDATE task_scheduler_status SET status = ?, updated_at = ? WHERE task_name = ?"
		args = []interface{}{value, time.Now(), t.name}
	case "enabled":
		status := "stopped"
		if value.(bool) {
			status = "stopped" // 启用但未运行
		}
		query = "UPDATE task_scheduler_status SET status = ?, updated_at = ? WHERE task_name = ?"
		args = []interface{}{status, time.Now(), t.name}
	case "last_error":
		query = "UPDATE task_scheduler_status SET last_error = ?, error_count = error_count + 1, updated_at = ? WHERE task_name = ?"
		args = []interface{}{value, time.Now(), t.name}
	case "run_count":
		query = "UPDATE task_scheduler_status SET run_count = run_count + 1, last_run_at = ?, updated_at = ? WHERE task_name = ?"
		args = []interface{}{time.Now(), time.Now(), t.name}
	default:
		return
	}

	_, err := t.db.GetDB().Exec(query, args...)
	if err != nil {
		t.logger.Error("更新任务状态失败", "field", field, "error", err)
	}
}

// updateVerificationStatistics 更新验证统计信息
func (t *MailboxVerificationTask) updateVerificationStatistics(successCount, failedCount int) {
	today := time.Now().Format("2006-01-02")

	// 更新或插入今日统计
	query := `
		INSERT OR REPLACE INTO mailbox_statistics (
			stat_date, total_accounts, verified_accounts, failed_accounts, created_at
		) VALUES (
			?, 
			(SELECT COUNT(*) FROM accounts),
			(SELECT COUNT(*) FROM accounts WHERE verification_status = 'verified'),
			(SELECT COUNT(*) FROM accounts WHERE verification_status = 'failed'),
			?
		)
	`

	_, err := t.db.GetDB().Exec(query, today, time.Now())
	if err != nil {
		t.logger.Error("更新验证统计失败", "error", err)
	}
}
