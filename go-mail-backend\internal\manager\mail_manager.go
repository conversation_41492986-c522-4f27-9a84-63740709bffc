package manager

import (
	"context"
	"go-mail/internal/database"
	"go-mail/internal/errors"
	"go-mail/internal/interfaces"
	"go-mail/internal/mail/client"
	"go-mail/internal/pool"
	"go-mail/internal/types"
	"log/slog"
	"net/http"
	"sync"
	"time"
)

// MailManager 邮件管理系统实现
type MailManager struct {
	config       *types.ManagerConfig
	accountPool  interfaces.AccountPool
	sessionPool  interfaces.SessionPool
	proxyManager interfaces.ProxyManager
	loginClient  interfaces.LoginClient
	database     *database.Database
	logger       *slog.Logger

	// 运行状态
	running   bool
	startTime time.Time
	mutex     sync.RWMutex

	// 统计信息
	stats      *Statistics
	statsMutex sync.RWMutex

	// 生命周期管理
	ctx    context.Context
	cancel context.CancelFunc
}

// Statistics 内部统计信息
type Statistics struct {
	TotalLogins      int
	SuccessfulLogins int
	FailedLogins     int
	TotalLoginTime   time.Duration
}

// NewMailManager 创建新的邮件管理器
func NewMailManager(config *types.ManagerConfig) *MailManager {
	if config == nil {
		config = types.DefaultManagerConfig()
	}

	// 创建日志器
	logger := slog.Default()

	// 初始化数据库
	db, err := database.NewDatabase("data/accounts.db")
	if err != nil {
		logger.Error("初始化数据库失败", "error", err)
		// 继续运行，但数据库功能将不可用
		db = nil
	} else {
		logger.Info("数据库初始化成功", "path", "data/accounts.db")
	}

	// 创建组件
	accountPool := pool.NewAccountPool()
	sessionPool := pool.NewSessionPool(config.SessionTimeout)
	proxyManager := pool.NewProxyManager()
	loginClient := client.NewLoginClient(config)

	return &MailManager{
		config:       config,
		accountPool:  accountPool,
		sessionPool:  sessionPool,
		proxyManager: proxyManager,
		loginClient:  loginClient,
		database:     db,
		logger:       logger,
		stats:        &Statistics{},
	}
}

// Start 启动管理器
func (m *MailManager) Start(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.running {
		return errors.NewConfigurationError(errors.ErrCodeConfigConflict, "管理器已在运行", nil)
	}

	// 创建上下文
	m.ctx, m.cancel = context.WithCancel(ctx)
	m.running = true
	m.startTime = time.Now()

	// 启动后台任务
	go m.backgroundTasks()

	m.logger.Info("邮件管理器已启动", "config", m.config)
	return nil
}

// Stop 停止管理器
func (m *MailManager) Stop(ctx context.Context) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.running {
		return errors.NewConfigurationError(errors.ErrCodeConfigConflict, "管理器未在运行", nil)
	}

	// 取消上下文
	if m.cancel != nil {
		m.cancel()
	}

	// 关闭数据库连接
	if m.database != nil {
		if err := m.database.Close(); err != nil {
			m.logger.Warn("关闭数据库连接失败", "error", err)
		} else {
			m.logger.Info("数据库连接已关闭")
		}
	}

	m.running = false
	m.logger.Info("邮件管理器已停止")
	return nil
}

// IsRunning 检查管理器是否在运行
func (m *MailManager) IsRunning() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.running
}

// BatchLogin 批量登录
func (m *MailManager) BatchLogin(ctx context.Context, accounts []types.Account) (*types.BatchResult, error) {
	if !m.IsRunning() {
		return nil, errors.NewConfigurationError(errors.ErrCodeConfigInvalid, "管理器未启动", nil)
	}

	startTime := time.Now()
	result := &types.BatchResult{
		Total:   len(accounts),
		Results: make(map[string]*types.LoginResult),
		Errors:  make(map[string]error),
	}

	// 创建工作池
	semaphore := make(chan struct{}, m.config.MaxConcurrent)
	var wg sync.WaitGroup
	var resultMutex sync.Mutex

	// 并发执行登录
	for _, account := range accounts {
		wg.Add(1)
		go func(acc types.Account) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 执行登录
			loginResult, err := m.performSingleLogin(ctx, acc)

			// 更新结果
			resultMutex.Lock()
			if err != nil {
				result.Failed++
				result.Errors[acc.Username] = err
				m.updateStats(false, 0)
			} else {
				result.Success++
				result.Results[acc.Username] = loginResult
				m.updateStats(true, time.Since(startTime))
			}
			resultMutex.Unlock()

		}(account)
	}

	// 等待所有任务完成
	wg.Wait()
	result.Duration = time.Since(startTime)

	m.logger.Info("批量登录完成",
		"total", result.Total,
		"success", result.Success,
		"failed", result.Failed,
		"duration", result.Duration)

	return result, nil
}

// performSingleLogin 执行单个账户登录
func (m *MailManager) performSingleLogin(ctx context.Context, account types.Account) (*types.LoginResult, error) {
	// 添加账户到池中（如果不存在）
	if err := m.accountPool.Add(account); err != nil {
		// 如果账户已存在，忽略错误
		if errors.GetErrorCode(err) != errors.ErrCodeValidationConflict {
			return nil, err
		}
	}

	// 获取代理（如果启用代理轮换）
	var proxy *types.ProxyConfig
	if m.config.ProxyRotation {
		if p, err := m.proxyManager.GetNext(); err == nil {
			proxy = p
		}
	}

	// 创建会话
	session, err := m.sessionPool.Create(account, proxy)
	if err != nil {
		return nil, err
	}

	// 执行登录
	loginResult, err := m.loginClient.Login(ctx, account, proxy)
	if err != nil {
		// 登录失败，移除会话
		m.sessionPool.Remove(session.ID)
		m.accountPool.UpdateLoginInfo(account.Username, false)
		return nil, err
	}

	// 登录成功，更新会话信息
	err = m.sessionPool.UpdateSession(session.ID, loginResult.JSessionID, loginResult.NavigatorSID, loginResult.RedirectURL)
	if err != nil {
		m.logger.Warn("更新会话信息失败", "session_id", session.ID, "error", err)
	}

	// 为会话设置HTTP客户端（使用登录时获得的cookies）
	if httpClient, err := m.createHTTPClientForSession(proxy, loginResult.Cookies); err == nil {
		if err := m.sessionPool.SetClient(session.ID, httpClient); err != nil {
			m.logger.Warn("设置会话HTTP客户端失败", "session_id", session.ID, "error", err)
		}
	}

	// 更新账户登录信息
	m.accountPool.UpdateLoginInfo(account.Username, true)

	// 保存账户信息到数据库
	if m.database != nil {
		err = m.database.SaveAccount(
			account.Username,
			account.Password,
			loginResult.Cookies,
			session.ID,
			loginResult.JSessionID,
			loginResult.NavigatorSID,
		)
		if err != nil {
			m.logger.Warn("保存账户信息到数据库失败", "email", account.Username, "error", err)
		} else {
			m.logger.Info("账户信息已保存到数据库", "email", account.Username)
		}
	}

	// 设置会话ID
	loginResult.SessionID = session.ID

	return loginResult, nil
}

// BatchLogout 批量登出
func (m *MailManager) BatchLogout(ctx context.Context, sessionIDs []string) error {
	if !m.IsRunning() {
		return errors.NewConfigurationError(errors.ErrCodeConfigInvalid, "管理器未启动", nil)
	}

	var wg sync.WaitGroup
	semaphore := make(chan struct{}, m.config.MaxConcurrent)

	for _, sessionID := range sessionIDs {
		wg.Add(1)
		go func(id string) {
			defer wg.Done()

			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 获取会话
			session, err := m.sessionPool.Get(id)
			if err != nil {
				m.logger.Warn("获取会话失败", "session_id", id, "error", err)
				return
			}

			// 执行登出
			if err := m.loginClient.Logout(ctx, session); err != nil {
				m.logger.Warn("登出失败", "session_id", id, "error", err)
			}

			// 移除会话
			m.sessionPool.Remove(id)
		}(sessionID)
	}

	wg.Wait()
	return nil
}

// BatchHealthCheck 批量健康检查
func (m *MailManager) BatchHealthCheck(ctx context.Context) (*types.HealthReport, error) {
	if !m.IsRunning() {
		return nil, errors.NewConfigurationError(errors.ErrCodeConfigInvalid, "管理器未启动", nil)
	}

	report := &types.HealthReport{
		Timestamp:     time.Now(),
		OverallStatus: "healthy",
		AccountStatus: make(map[string]types.AccountStatus),
		SessionStatus: make(map[string]types.SessionStatus),
		ProxyStatus:   make(map[string]bool),
	}

	// 检查账户状态
	accounts := m.accountPool.GetAccountInfo()
	for _, account := range accounts {
		report.AccountStatus[account.Username] = account.Status
	}

	// 检查会话状态
	sessions := m.sessionPool.GetActive()
	for _, session := range sessions {
		report.SessionStatus[session.ID] = session.Status
	}

	// 检查代理状态
	proxyStatuses := m.proxyManager.GetProxyStatus()
	for _, status := range proxyStatuses {
		report.ProxyStatus[status.ID] = status.Enabled
	}

	// 清理过期会话
	m.sessionPool.Cleanup()

	return report, nil
}

// updateStats 更新统计信息
func (m *MailManager) updateStats(success bool, duration time.Duration) {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()

	m.stats.TotalLogins++
	if success {
		m.stats.SuccessfulLogins++
		m.stats.TotalLoginTime += duration
	} else {
		m.stats.FailedLogins++
	}
}

// backgroundTasks 后台任务
func (m *MailManager) backgroundTasks() {
	ticker := time.NewTicker(m.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			// 执行定期清理和健康检查
			m.sessionPool.Cleanup()
			m.proxyManager.BatchHealthCheck()
		}
	}
}

// 实现剩余的接口方法

// AddAccount 添加账户
func (m *MailManager) AddAccount(account types.Account) error {
	return m.accountPool.Add(account)
}

// RemoveAccount 移除账户
func (m *MailManager) RemoveAccount(username string) error {
	// 先移除相关会话
	if session, err := m.sessionPool.GetByAccount(username); err == nil {
		m.sessionPool.Remove(session.ID)
	}
	return m.accountPool.Remove(username)
}

// GetAccountStatus 获取账户状态
func (m *MailManager) GetAccountStatus(username string) (*types.AccountStatus, error) {
	account, err := m.accountPool.Get(username)
	if err != nil {
		return nil, err
	}
	return &account.Status, nil
}

// ListAccounts 列出所有账户
func (m *MailManager) ListAccounts() []types.AccountInfo {
	return m.accountPool.GetAccountInfo()
}

// SetProxyPool 设置代理池
func (m *MailManager) SetProxyPool(proxies []types.ProxyConfig) error {
	// 清空现有代理
	m.proxyManager.Clear()

	// 添加新代理
	for _, proxy := range proxies {
		if err := m.proxyManager.Add(proxy); err != nil {
			return err
		}
	}
	return nil
}

// RotateProxy 轮换代理
func (m *MailManager) RotateProxy(sessionID string) error {
	// 获取新代理
	proxy, err := m.proxyManager.GetNext()
	if err != nil {
		return err
	}

	// 这里应该更新会话的代理配置
	// 简化实现：记录日志
	m.logger.Info("代理轮换", "session_id", sessionID, "new_proxy", proxy.ID)
	return nil
}

// GetProxyStatus 获取代理状态
func (m *MailManager) GetProxyStatus() []types.ProxyStatus {
	return m.proxyManager.GetProxyStatus()
}

// UpdateConfig 更新配置
func (m *MailManager) UpdateConfig(config *types.ManagerConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.config = config
	m.logger.Info("配置已更新", "config", config)
	return nil
}

// GetConfig 获取配置
func (m *MailManager) GetConfig() *types.ManagerConfig {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 返回副本
	configCopy := *m.config
	return &configCopy
}

// GetSessionByAccount 根据账户名获取会话
func (m *MailManager) GetSessionByAccount(username string) (*types.Session, error) {
	return m.sessionPool.GetByAccount(username)
}

// {{ AURA-X: Modify - 修改方法签名以支持cookies参数. Approval: 寸止(ID:**********). }}
// createHTTPClientForSession 为会话创建HTTP客户端
func (m *MailManager) createHTTPClientForSession(proxyConfig *types.ProxyConfig, cookies []*http.Cookie) (*http.Client, error) {
	// 使用LoginClient的CreateHTTPClientWithCookies方法
	loginClient := m.loginClient.(*client.LoginClient)
	return loginClient.CreateHTTPClientWithCookies(proxyConfig, cookies)
}

// GetStatistics 获取统计信息
func (m *MailManager) GetStatistics() *types.Statistics {
	m.statsMutex.RLock()
	defer m.statsMutex.RUnlock()

	avgLoginTime := time.Duration(0)
	if m.stats.SuccessfulLogins > 0 {
		avgLoginTime = m.stats.TotalLoginTime / time.Duration(m.stats.SuccessfulLogins)
	}

	uptime := time.Duration(0)
	if !m.startTime.IsZero() {
		uptime = time.Since(m.startTime)
	}

	return &types.Statistics{
		TotalAccounts:    m.accountPool.Count(),
		ActiveSessions:   len(m.sessionPool.GetActive()),
		TotalLogins:      m.stats.TotalLogins,
		SuccessfulLogins: m.stats.SuccessfulLogins,
		FailedLogins:     m.stats.FailedLogins,
		AverageLoginTime: avgLoginTime,
		Uptime:           uptime,
	}
}

// GetLogs 获取日志（简化实现）
func (m *MailManager) GetLogs(filter types.LogFilter) []types.LogEntry {
	// 这里应该实现真正的日志查询
	// 简化实现：返回空列表
	return []types.LogEntry{}
}
